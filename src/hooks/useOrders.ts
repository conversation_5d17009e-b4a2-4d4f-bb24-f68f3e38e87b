import { useState, useEffect, useCallback, useRef } from 'react'
import { QueryDocumentSnapshot, DocumentData, Unsubscribe } from 'firebase/firestore'
import { OrderService, CreateOrderData, OrderFilters } from '@/services/OrderService'
import { OrderDocument, OrderStatus } from '@/types/database'
import toast from 'react-hot-toast'

export interface UseOrdersReturn {
  // State
  orders: OrderDocument[]
  currentOrder: OrderDocument | null
  loading: boolean
  error: string | null
  hasMore: boolean
  
  // Actions
  createOrder: (orderData: CreateOrderData) => Promise<string | null>
  updateOrderStatus: (orderId: string, status: OrderStatus, notes?: string) => Promise<void>
  loadOrders: (filters?: OrderFilters) => Promise<void>
  loadMoreOrders: () => Promise<void>
  subscribeToOrder: (orderId: string) => void
  subscribeToUserOrders: (userId: string, userType: 'customer' | 'driver') => void
  unsubscribeAll: () => void
  refreshOrders: () => Promise<void>
  
  // Filters
  filters: OrderFilters
  setFilters: (filters: OrderFilters) => void
}

export function useOrders(): UseOrdersReturn {
  // State
  const [orders, setOrders] = useState<OrderDocument[]>([])
  const [currentOrder, setCurrentOrder] = useState<OrderDocument | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [filters, setFilters] = useState<OrderFilters>({})
  
  // Refs for pagination and subscriptions
  const lastDocRef = useRef<QueryDocumentSnapshot<DocumentData> | undefined>()
  const subscriptionsRef = useRef<Unsubscribe[]>([])
  const isInitialLoadRef = useRef(true)

  // Create order
  const createOrder = useCallback(async (orderData: CreateOrderData): Promise<string | null> => {
    try {
      setLoading(true)
      setError(null)
      
      const orderId = await OrderService.createOrder(orderData)
      
      toast.success('Ride request created successfully!')
      
      // Refresh orders to include the new one
      await loadOrders(filters)
      
      return orderId
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create order'
      setError(errorMessage)
      toast.error(errorMessage)
      return null
    } finally {
      setLoading(false)
    }
  }, [filters])

  // Update order status
  const updateOrderStatus = useCallback(async (
    orderId: string, 
    status: OrderStatus, 
    notes?: string
  ): Promise<void> => {
    try {
      setError(null)
      
      await OrderService.updateOrderStatus(orderId, status, notes)
      
      // Update local state optimistically
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === orderId 
            ? { ...order, status: { ...order.status, current: status } }
            : order
        )
      )
      
      if (currentOrder?.id === orderId) {
        setCurrentOrder(prev => 
          prev ? { ...prev, status: { ...prev.status, current: status } } : null
        )
      }
      
      toast.success(`Order status updated to ${status}`)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update order status'
      setError(errorMessage)
      toast.error(errorMessage)
    }
  }, [currentOrder])

  // Load orders with filters
  const loadOrders = useCallback(async (newFilters?: OrderFilters): Promise<void> => {
    try {
      setLoading(true)
      setError(null)
      
      const filtersToUse = newFilters || filters
      const result = await OrderService.getOrders(filtersToUse, undefined, 20)
      
      setOrders(result.orders)
      setHasMore(result.hasMore)
      lastDocRef.current = result.lastDoc
      
      if (newFilters) {
        setFilters(newFilters)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load orders'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [filters])

  // Load more orders (pagination)
  const loadMoreOrders = useCallback(async (): Promise<void> => {
    if (!hasMore || loading) return

    try {
      setLoading(true)
      setError(null)
      
      const result = await OrderService.getOrders(filters, lastDocRef.current, 20)
      
      setOrders(prevOrders => [...prevOrders, ...result.orders])
      setHasMore(result.hasMore)
      lastDocRef.current = result.lastDoc
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load more orders'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [filters, hasMore, loading])

  // Unsubscribe from all real-time listeners
  const unsubscribeAll = useCallback((): void => {
    subscriptionsRef.current.forEach(unsubscribe => unsubscribe())
    subscriptionsRef.current = []
  }, [])

  // Subscribe to a specific order
  const subscribeToOrder = useCallback((orderId: string): void => {
    // Unsubscribe from previous subscriptions
    unsubscribeAll()

    const unsubscribe = OrderService.subscribeToOrder(orderId, (order) => {
      setCurrentOrder(order)

      // Also update the order in the orders list if it exists
      if (order) {
        setOrders(prevOrders =>
          prevOrders.map(o => o.id === order.id ? order : o)
        )
      }
    })

    subscriptionsRef.current.push(unsubscribe)
  }, [unsubscribeAll])

  // Subscribe to user orders (customer or driver)
  const subscribeToUserOrders = useCallback((userId: string, userType: 'customer' | 'driver'): void => {
    // Unsubscribe from previous subscriptions
    unsubscribeAll()

    const unsubscribe = OrderService.subscribeToUserOrders(userId, userType, (orders) => {
      setOrders(orders)
      setLoading(false)
    })

    subscriptionsRef.current.push(unsubscribe)
  }, [unsubscribeAll])

  // Refresh orders
  const refreshOrders = useCallback(async (): Promise<void> => {
    try {
      setLoading(true)
      setError(null)
      lastDocRef.current = undefined

      const result = await OrderService.getOrders(filters, undefined, 20)
      setOrders(result.orders)
      setHasMore(result.hasMore)
      lastDocRef.current = result.lastDoc
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh orders'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [filters])

  // Update filters
  const setFiltersCallback = useCallback(async (newFilters: OrderFilters): Promise<void> => {
    try {
      setLoading(true)
      setError(null)
      setFilters(newFilters)
      lastDocRef.current = undefined

      const result = await OrderService.getOrders(newFilters, undefined, 20)
      setOrders(result.orders)
      setHasMore(result.hasMore)
      lastDocRef.current = result.lastDoc
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load orders'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  // Initial load
  useEffect(() => {
    if (isInitialLoadRef.current) {
      isInitialLoadRef.current = false

      // Direct call to avoid dependency issues - use initial filters
      const initialLoad = async () => {
        try {
          setLoading(true)
          setError(null)

          // Use the initial filters value to avoid dependency issues
          const initialFilters: OrderFilters = {
            status: undefined,
            customerId: undefined,
            driverId: undefined,
            dateFrom: undefined,
            dateTo: undefined,
            search: undefined
          }

          const result = await OrderService.getOrders(initialFilters, undefined, 20)
          setOrders(result.orders)
          setHasMore(result.hasMore)
          lastDocRef.current = result.lastDoc
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to load orders'
          setError(errorMessage)
          toast.error(errorMessage)
        } finally {
          setLoading(false)
        }
      }

      initialLoad()
    }
  }, []) // Empty dependency array to run only once

  // Cleanup subscriptions on unmount
  useEffect(() => {
    return () => {
      unsubscribeAll()
    }
  }, [unsubscribeAll])

  return {
    // State
    orders,
    currentOrder,
    loading,
    error,
    hasMore,
    
    // Actions
    createOrder,
    updateOrderStatus,
    loadOrders,
    loadMoreOrders,
    subscribeToOrder,
    subscribeToUserOrders,
    unsubscribeAll,
    refreshOrders,
    
    // Filters
    filters,
    setFilters: setFiltersCallback
  }
}

export default useOrders
